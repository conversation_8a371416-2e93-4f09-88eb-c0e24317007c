#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片包分配器
根据Excel配置文件将barcode文件夹下的图片按比例分配到不同的package中
确保每张图片只分配到一个package中
"""

import pandas as pd
import os
import shutil
import random
from pathlib import Path
from typing import Dict, List, Tuple
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ImagePackageDistributor:
    def __init__(self, excel_file: str, barcode_folder: str, output_folder: str = "packages"):
        """
        初始化图片包分配器

        Args:
            excel_file: Excel配置文件路径
            barcode_folder: 包含图片的barcode文件夹路径
            output_folder: 输出文件夹路径
        """
        self.excel_file = excel_file
        self.barcode_folder = barcode_folder
        self.output_folder = output_folder
        self.sheet_names = []
        self.folder_mapping = {}
        
    def load_config(self):
        """加载Excel配置文件，获取所有sheet名称"""
        try:
            # 获取Excel文件中的所有sheet名称
            xl_file = pd.ExcelFile(self.excel_file)
            self.sheet_names = xl_file.sheet_names
            logger.info(f"找到的sheet名称: {self.sheet_names}")

        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            raise
    
    def build_folder_mapping(self):
        """构建文件夹名称映射"""
        # 获取barcode文件夹下的所有子文件夹
        barcode_folders = [f for f in os.listdir(self.barcode_folder) 
                          if os.path.isdir(os.path.join(self.barcode_folder, f))]
        
        # 构建映射关系：库区别名 -> 实际文件夹名
        self.folder_mapping = {}
        
        for folder_name in barcode_folders:
            # 找到第一个'-'符号，忽略前面的部分
            if '-' in folder_name:
                suffix = folder_name[folder_name.index('-') + 1:]
                self.folder_mapping[suffix] = folder_name
        
        logger.info(f"构建文件夹映射完成，共{len(self.folder_mapping)}个映射")
        
    def get_images_from_folder(self, folder_path: str) -> List[str]:
        """获取文件夹中的所有图片文件"""
        image_extensions = {'.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.gif'}
        images = []
        
        if os.path.exists(folder_path):
            for file in os.listdir(folder_path):
                if any(file.lower().endswith(ext) for ext in image_extensions):
                    images.append(os.path.join(folder_path, file))
        
        return images
    
    def sanitize_folder_name(self, name: str) -> str:
        """清理文件夹名称，替换不合法的字符"""
        # 替换不合法的文件夹名称字符
        illegal_chars = ['/', '\\', ':', '*', '?', '"', '<', '>', '|']
        sanitized = name
        for char in illegal_chars:
            sanitized = sanitized.replace(char, '_')
        return sanitized

    def process_single_sheet(self, sheet_name: str) -> Dict[str, int]:
        """处理单个sheet的配置并分配图片"""
        logger.info(f"开始处理sheet: {sheet_name}")

        try:
            # 读取指定sheet的配置
            config_df = pd.read_excel(self.excel_file, sheet_name=sheet_name, header=None)
            logger.info(f"Sheet {sheet_name} 数据形状: {config_df.shape}")

            # 获取package名称（第一行，从第2列开始）
            package_names = []
            for col_idx in range(1, config_df.shape[1]):  # 从第2列开始
                package_name = config_df.iloc[0, col_idx]
                if pd.notna(package_name):
                    package_names.append(str(package_name))

            logger.info(f"Sheet {sheet_name} 中找到的package名称: {package_names}")

            # 为当前sheet创建输出文件夹
            sheet_output_folder = os.path.join(self.output_folder, self.sanitize_folder_name(sheet_name))
            os.makedirs(sheet_output_folder, exist_ok=True)

            # 为每个package创建文件夹
            for package_name in package_names:
                safe_package_name = self.sanitize_folder_name(package_name)
                package_folder = os.path.join(sheet_output_folder, safe_package_name)
                os.makedirs(package_folder, exist_ok=True)

            # 统计信息
            distribution_summary = {package: 0 for package in package_names}

            # 遍历配置中的每一行（从第3行开始，跳过标题行）
            for row_idx in range(2, len(config_df)):
                area_name = config_df.iloc[row_idx, 0]  # 库区别名

                if pd.isna(area_name):
                    continue

                area_name = str(area_name)

                # 检查是否有对应的文件夹
                if area_name not in self.folder_mapping:
                    logger.warning(f"未找到库区 {area_name} 对应的文件夹")
                    continue

                actual_folder_name = self.folder_mapping[area_name]
                folder_path = os.path.join(self.barcode_folder, actual_folder_name)

                # 获取该文件夹中的所有图片
                images = self.get_images_from_folder(folder_path)
                if not images:
                    logger.warning(f"文件夹 {actual_folder_name} 中没有找到图片")
                    continue

                # 随机打乱图片列表，确保随机分配
                random.shuffle(images)

                logger.info(f"处理库区 {area_name} ({actual_folder_name})，共 {len(images)} 张图片")

                # 获取每个package需要的图片数量
                image_index = 0
                for col_idx, package_name in enumerate(package_names):
                    package_count = config_df.iloc[row_idx, col_idx + 1]  # 从第2列开始

                    if pd.isna(package_count) or package_count == 0:
                        continue

                    # 转换为整数
                    try:
                        count = int(float(package_count))
                    except (ValueError, TypeError):
                        logger.warning(f"无法解析数量: {package_count}")
                        continue

                    if count <= 0:
                        continue

                    # 检查是否有足够的图片
                    if image_index + count > len(images):
                        available = len(images) - image_index
                        logger.warning(f"库区 {area_name} package {package_name} 需要 {count} 张图片，但只剩 {available} 张")
                        count = available

                    if count <= 0:
                        break

                    # 复制图片到对应的package文件夹
                    safe_package_name = self.sanitize_folder_name(package_name)
                    package_folder = os.path.join(sheet_output_folder, safe_package_name)

                    for _ in range(count):
                        if image_index >= len(images):
                            break

                        src_image = images[image_index]
                        image_filename = os.path.basename(src_image)

                        # 为了避免文件名冲突，添加库区前缀
                        new_filename = f"{area_name}_{image_filename}"
                        dst_image = os.path.join(package_folder, new_filename)

                        try:
                            shutil.copy2(src_image, dst_image)
                            image_index += 1
                            distribution_summary[package_name] += 1
                        except Exception as e:
                            logger.error(f"复制图片失败: {src_image} -> {dst_image}, 错误: {e}")

                    logger.info(f"  -> {package_name}: 分配了 {count} 张图片")

            return distribution_summary

        except Exception as e:
            logger.error(f"处理sheet {sheet_name} 失败: {e}")
            raise

    def distribute_images(self):
        """分配图片到各个package - 处理所有sheet"""
        # 创建输出文件夹
        os.makedirs(self.output_folder, exist_ok=True)

        # 总体统计信息
        total_images_processed = 0
        all_sheets_summary = {}
        
        # 处理每个sheet
        for sheet_name in self.sheet_names:
            logger.info(f"开始处理sheet: {sheet_name}")
            sheet_summary = self.process_single_sheet(sheet_name)
            all_sheets_summary[sheet_name] = sheet_summary

            # 累计总图片数
            sheet_total = sum(sheet_summary.values())
            total_images_processed += sheet_total
            logger.info(f"Sheet {sheet_name} 完成，共处理 {sheet_total} 张图片")

        # 输出总体统计信息
        logger.info("=" * 50)
        logger.info("所有sheet分配完成！总体统计信息:")
        logger.info(f"总共处理图片: {total_images_processed} 张")

        for sheet_name, sheet_summary in all_sheets_summary.items():
            logger.info(f"\nSheet: {sheet_name}")
            for package_name, count in sheet_summary.items():
                logger.info(f"  {package_name}: {count} 张图片")

        logger.info("=" * 50)

        return all_sheets_summary
    
    def run(self):
        """运行图片分配流程"""
        logger.info("开始多sheet图片包分配流程...")

        # 1. 加载配置（获取所有sheet名称）
        self.load_config()

        # 2. 构建文件夹映射
        self.build_folder_mapping()

        # 3. 分配图片（处理所有sheet）
        summary = self.distribute_images()

        logger.info("多sheet图片包分配流程完成！")
        return summary


def main():
    """主函数"""
    # 配置参数
    excel_file = "照片分类.xlsx"
    barcode_folder = "barcode"
    output_folder = "packages"
    
    # 检查文件是否存在
    if not os.path.exists(excel_file):
        print(f"错误: Excel文件 {excel_file} 不存在")
        return
    
    if not os.path.exists(barcode_folder):
        print(f"错误: barcode文件夹 {barcode_folder} 不存在")
        return
    
    # 创建分配器并运行
    distributor = ImagePackageDistributor(excel_file, barcode_folder, output_folder)
    
    try:
        summary = distributor.run()
        print("\n分配结果:")
        total_images = 0
        for sheet_name, sheet_summary in summary.items():
            print(f"\nSheet: {sheet_name}")
            sheet_total = 0
            for package_name, count in sheet_summary.items():
                print(f"  {package_name}: {count} 张图片")
                sheet_total += count
            print(f"  Sheet小计: {sheet_total} 张图片")
            total_images += sheet_total

        print(f"\n总计: {total_images} 张图片")

    except Exception as e:
        logger.error(f"运行失败: {e}")
        raise


if __name__ == "__main__":
    main()
